from employee_data import get_employee_leave_info
from policy_faiss import build_policy_faiss, search_policy
from azure_openai import ask_gpt4o

# Main chatbot logic
def chatbot_query(employee_id, year, month, query):
    # Get employee leave info
    emp_info = get_employee_leave_info(employee_id, year, month)
    if not emp_info:
        return "Employee record not found."

    # Search policy
    index, chunks = build_policy_faiss()
    policy_chunks = search_policy(query, index, chunks)
    policy_text = " ".join(policy_chunks)

    # Optimized prompt for concise, definitive answers
    prompt = f"""EMPLOYEE DATA: {emp_info}
POLICY: {policy_text}
QUESTION: {query}

INSTRUCTIONS:
- Give EXACTLY ONE definitive answer
- Maximum 15 words total
- Start with a number or action word
- Use ONLY these formats:
  * "You can take X days in [month]"
  * "X days remaining for [year]"
  * "Apply by [date] for approval"
  * "Maximum X days allowed"
  * "Insufficient data" (if unclear)
- NO explanations, context, or additional text

ANSWER:"""
    return ask_gpt4o(prompt)

if __name__ == "__main__":
    # Example chatbot query
    print(chatbot_query('E001', 2025, 'May', 'How many annual leaves can I take in November?'))
