from crewai import Agent, Task, Crew
import pandas as pd
import os
from dotenv import load_dotenv
import openai
import faiss
import numpy as np

# Load environment variables
load_dotenv()

openai.api_type = "azure"
openai.api_key = os.getenv("AZURE_OPENAI_API_KEY")
openai.api_base = os.getenv("AZURE_OPENAI_ENDPOINT")
openai.api_version = os.getenv("AZURE_OPENAI_API_VERSION")
DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")

EMPLOYEE_XLSX = 'GSANZ_HRMS_Leave_Report_Sample.xlsx'
POLICY_PDF = 'policies/GSANZ_Final_Leave_Policy.pdf'

# --- AGENTS ---

# 1. Employee Data Agent
import pandas as pd

def get_employee_leave_info(employee_id, year, month):
    df = pd.read_excel(EMPLOYEE_XLSX)
    record = df[(df['EmployeeID'] == employee_id) & (df['Year'] == year) & (df['Month'] == month)]
    if record.empty:
        return None
    return record.iloc[0].to_dict()

employee_agent = Agent(
    name="EmployeeDataAgent",
    description="Fetches employee leave data from CSV.",
    fn=get_employee_leave_info
)

# 2. Policy Agent
from PyPDF2 import PdfReader

def extract_policy_text():
    reader = PdfReader(POLICY_PDF)
    text = ""
    for page in reader.pages:
        text += page.extract_text() + "\n"
    return text

def get_embedding(text):
    response = openai.Embedding.create(
        input=[text],
        engine=DEPLOYMENT_NAME
    )
    return np.array(response['data'][0]['embedding'], dtype=np.float32)

def build_policy_faiss():
    text = extract_policy_text()
    chunks = [text[i:i+512] for i in range(0, len(text), 512)]
    embeddings = [get_embedding(chunk) for chunk in chunks]
    dim = len(embeddings[0])
    index = faiss.IndexFlatL2(dim)
    index.add(np.stack(embeddings))
    return index, chunks

def search_policy(query, index, chunks, top_k=1):
    query_emb = get_embedding(query)
    D, I = index.search(np.expand_dims(query_emb, axis=0), top_k)
    return [chunks[i] for i in I[0]]

def policy_agent_fn(query):
    index, chunks = build_policy_faiss()
    return search_policy(query, index, chunks)

policy_agent = Agent(
    name="PolicyAgent",
    description="Searches and interprets leave policy from PDF using FAISS.",
    fn=policy_agent_fn
)

# 3. Chatbot Agent
def chatbot_agent_fn(employee_id, query, year=None, month=None):
    """
    Generic HR chatbot function that handles all HR domains and question types.
    Automatically classifies queries and provides appropriate responses.
    """
    from generic_hr_system import GenericHRSystem

    # Initialize the generic HR system
    hr_system = GenericHRSystem()

    # Build filters from optional parameters
    filters = {}
    if year:
        filters['Year'] = year
    if month:
        filters['Month'] = month

    # Process the query using the generic system
    try:
        response = hr_system.process_hr_query(employee_id, query, **filters)
        return response
    except Exception as e:
        return "System error - contact HR"

chatbot_agent = Agent(
    name="ChatbotAgent",
    description="Orchestrates the conversation and generates the final answer.",
    fn=chatbot_agent_fn
)

# --- CREW SETUP ---

from agents.agents import employee_agent, policy_agent, chatbot_agent
from agents.tasks import fetch_employee_leave_task, fetch_policy_task, answer_employee_query_task
from crewai import Crew

crew = Crew(
    agents=[employee_agent, policy_agent, chatbot_agent],
    tasks=[fetch_employee_leave_task, fetch_policy_task, answer_employee_query_task]
)

def ask_hrms_chatbot(employee_id, query, year=None, month=None):
    """
    Main entry point for HR queries. Handles any HR domain and question type.

    Args:
        employee_id: Employee ID
        query: Natural language HR question
        year: Optional year filter
        month: Optional month filter

    Returns:
        Concise, definitive answer (max 15 words)
    """
    return chatbot_agent_fn(employee_id, query, year, month)

if __name__ == "__main__":
    print("=== DUAL-MODE HR SYSTEM DEMO ===\n")

    # Test 1: Simple queries (concise responses)
    print("🔹 SIMPLE QUERIES (Concise responses)")
    print("-" * 50)
    simple_queries = [
        ('E001', 'How many annual leaves can I take in November?'),
        ('E001', 'What is my health insurance contribution?'),
        ('E001', 'When is my next performance review?'),
        ('E001', 'What is my current salary?'),
    ]

    for employee_id, query in simple_queries:
        print(f"Query: {query}")
        response = ask_hrms_chatbot(employee_id, query)
        print(f"Response: {response}")
        print(f"Words: {len(response.split())}")
        print("-" * 30)

    print("\n🔹 STRATEGIC PLANNING QUERIES (Detailed advice)")
    print("-" * 50)

    # Test 2: Strategic planning queries (detailed responses)
    planning_queries = [
        ('E001', 'How can I plan my leave in August so that I get 10 days and still have to apply minimum days?'),
        ('E001', 'Best way to get 2 weeks off in December using least leave days?'),
    ]

    for employee_id, query in planning_queries:
        print(f"Query: {query}")
        response = ask_hrms_chatbot(employee_id, query)
        print(f"Response:\n{response}")
        print("=" * 60)
