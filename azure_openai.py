import os
import openai
from dotenv import load_dotenv

load_dotenv()

DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")

# Use the new openai>=1.0.0 API for Azure OpenAI

def ask_gpt4o(prompt):
    client = openai.AzureOpenAI(
        api_key=os.getenv("AZURE_OPENAI_API_KEY"),
        api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
        azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT")
    )
    response = client.chat.completions.create(
        model=DEPLOYMENT_NAME,
        messages=[
            {"role": "system", "content": "You are a concise answer generator. Respond with maximum 15 words. No explanations."},
            {"role": "user", "content": prompt}
        ],
        max_tokens=50,  # Reduced from 500 to enforce brevity
        temperature=0.1,  # Low temperature for consistent responses
        top_p=0.9  # Focused responses
    )
    return response.choices[0].message.content.strip()

if __name__ == "__main__":
    print(ask_gpt4o("Summarize the leave policy for annual leaves."))
