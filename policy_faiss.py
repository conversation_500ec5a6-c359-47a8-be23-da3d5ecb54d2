import numpy as np
import faiss
from azure_openai import ask_gpt4o
from policy_data import extract_policy_text
import openai
import os
from dotenv import load_dotenv

load_dotenv()

openai.api_type = "azure"
openai.api_key = os.getenv("AZURE_OPENAI_API_KEY")
openai.api_base = os.getenv("AZURE_OPENAI_ENDPOINT")
openai.api_version = os.getenv("AZURE_OPENAI_API_VERSION")
DEPLOYMENT_NAME = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")

# Helper to get embeddings from Azure OpenAI
def get_embedding(text):
    response = openai.Embedding.create(
        input=[text],
        engine=DEPLOYMENT_NAME
    )
    return np.array(response['data'][0]['embedding'], dtype=np.float32)

if __name__ == "__main__":
    results = search_policy("leave policy for November", index, chunks)
    print(results)
