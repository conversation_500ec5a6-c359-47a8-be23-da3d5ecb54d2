import os
from dotenv import load_dotenv
from PyPDF2 import PdfReader

# Load environment variables
load_dotenv()

POLICY_PDF = 'policies/GSANZ_Formatted_Leave_Policy.pdf'

# Extract text from PDF
def extract_policy_text():
    reader = PdfReader(POLICY_PDF)
    text = ""
    for page in reader.pages:
        text += page.extract_text() + "\n"
    return text

if __name__ == "__main__":
    policy_text = extract_policy_text()
    print(policy_text[:500])  # Print first 500 chars
