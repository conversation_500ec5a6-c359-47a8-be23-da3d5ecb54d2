from crewai import Task

def create_hr_data_extraction_task(agent, hr_domain="general", data_type="general"):
    """Create a generic task for extracting HR data from any domain."""
    return Task(
        description=(
            f"Extract ONLY the specific {hr_domain} data requested from employee records. "
            f"Adapt format based on data type: "
            f"- Numbers/Balances: 'Value: X, Remaining: Y, Type: Z' "
            f"- Dates: 'Start: X, End: Y, Due: Z' "
            f"- Status: 'Status: X, Level: Y, Category: Z' "
            f"- Amounts: 'Amount: X, Limit: Y, Used: Z' "
            f"Return ONLY the requested data - no explanations."
        ),
        expected_output=(
            f"EXACT FORMAT based on {hr_domain} data type: "
            f"- Leave: 'Taken: X, Remaining: Y, Type: Z' "
            f"- Benefits: 'Plan: X, Coverage: Y, Cost: Z' "
            f"- Payroll: 'Salary: X, Bonus: Y, Deductions: Z' "
            f"- Performance: 'Rating: X, Goals: Y, Review: Z' "
            f"NO other text, explanations, or formatting allowed."
        ),
        agent=agent,
        output_file=f"{hr_domain}_data.txt"
    )

def create_hr_policy_lookup_task(agent, hr_domain="general", query_type="general"):
    """Create a generic task for retrieving HR policy information from any domain."""
    return Task(
        description=(
            f"Find the ONE {hr_domain} policy rule that answers the {query_type} question. "
            f"Search across ALL HR domains: leave, benefits, payroll, performance, compliance, training. "
            f"Return ONLY the specific number, limit, requirement, or rule - no explanations. "
            f"Examples: 'Maximum 20 days', '$500 limit', 'Minimum 6 months', 'Apply 2 weeks advance'"
        ),
        expected_output=(
            f"ONLY the specific {hr_domain} policy fact for {query_type} questions: "
            f"- Limits: 'Maximum X days/amount' "
            f"- Requirements: 'Minimum X months/years' "
            f"- Deadlines: 'Apply X weeks/days advance' "
            f"- Amounts: '$X per period' or 'X% of salary' "
            f"- Eligibility: 'After X months service' "
            f"NO policy sections, references, or explanations."
        ),
        agent=agent,
        output_file=f"{hr_domain}_policy.txt"
    )

def create_hr_answer_generation_task(agent, hr_domain="general", query_type="general", context_tasks=None):
    """Create a generic task for generating HR answers for any domain and question type."""
    return Task(
        description=(
            f"Generate ONE definitive answer for {hr_domain} {query_type} question using provided data and policy. "
            f"STRICT RULES: "
            f"1. Maximum 15 words total "
            f"2. Start with number, amount, or action word "
            f"3. No explanations or context "
            f"4. Adapt format to question type: "
            f"   - Balance: 'X days/amount remaining' "
            f"   - Eligibility: 'Eligible after X months/years' "
            f"   - Process: 'Apply via [method] by [date]' "
            f"   - Amount: '$X per [period]' or 'X% of salary' "
            f"   - Status: 'Status: [current state]' "
            f"   - Deadline: 'Due by [date]'"
        ),
        expected_output=(
            f"EXACTLY one format for {hr_domain} {query_type} (maximum 15 words): "
            f"- Leave Balance: 'X days remaining for [year]' "
            f"- Benefits Amount: '$X monthly premium' or 'X% employer contribution' "
            f"- Payroll Info: '$X salary' or '$X bonus due' "
            f"- Performance Status: 'Rating: X/5' or 'Review due [date]' "
            f"- Process: 'Apply via [system] by [date]' "
            f"- Eligibility: 'Eligible after X months service' "
            f"- Insufficient data' (if unclear) "
            f"NO other formats or additional text allowed."
        ),
        agent=agent,
        context=context_tasks or [],
        output_file=f"{hr_domain}_{query_type}_answer.txt"
    )

def create_hr_query_classification_task(agent):
    """Create a task for classifying HR queries by domain and type."""
    return Task(
        description=(
            "Classify the HR question into: "
            "1. HR Domain: leave, benefits, payroll, performance, compliance, training, recruitment, disciplinary "
            "2. Query Type: balance, eligibility, process, deadline, amount, status, requirement, limit "
            "Return ONLY the classification - no explanations."
        ),
        expected_output=(
            "EXACT FORMAT REQUIRED: 'Domain: [domain], Type: [type]' "
            "Examples: "
            "- 'Domain: leave, Type: balance' "
            "- 'Domain: benefits, Type: eligibility' "
            "- 'Domain: payroll, Type: amount' "
            "NO other text or explanations allowed."
        ),
        agent=agent,
        output_file="query_classification.txt"
    )

# Backward compatibility functions
def create_fetch_employee_leave_task(agent):
    """Backward compatibility - creates leave-specific data extraction task."""
    return create_hr_data_extraction_task(agent, hr_domain="leave")

def create_fetch_policy_task(agent):
    """Backward compatibility - creates leave-specific policy lookup task."""
    return create_hr_policy_lookup_task(agent, hr_domain="leave", query_type="general")

def create_answer_employee_query_task(agent, context_tasks=None):
    """Backward compatibility - creates leave-specific answer generation task."""
    return create_hr_answer_generation_task(agent, hr_domain="leave", query_type="general", context_tasks=context_tasks)

# Default task instances (for backward compatibility)
# These will be created with agents in crew_setup.py
fetch_employee_leave_task = None
fetch_policy_task = None
answer_employee_query_task = None
