# GSANZ HRMS Chatbot

This project implements a HRMS chatbot for GSANZ using CrewAI, GPT-4o (Azure OpenAI), and FAISS for semantic search.

## Features
- Employees can ask questions about their leave balance, leave policy, and optimal leave planning.
- Uses CrewAI agents for modular, maintainable orchestration of HRMS queries.
- Integrates with Azure OpenAI (GPT-4o) for natural language understanding and response generation.
- Uses FAISS for fast semantic search of policy documents.
- Employee data is sourced from an Excel file.

## Setup

1. **Install dependencies:**
   ```bash
   pip install --break-system-packages -r requirements.txt
   ```
2. **Add your Azure OpenAI credentials** to the `.env` file (already provided).
3. **Employee leave data:** Place your employee leave data in `GSANZ_HRMS_Leave_Report_Sample.xlsx`.
4. **Leave policy:** Place your leave policy PDF in `policies/GSANZ_Final_Leave_Policy.pdf`.

## Usage

### Streamlit Web App
Run the chatbot web interface:
```bash
streamlit run streamlit_app.py
```

### CLI Example
Run the CrewAI-based chatbot from the command line:
```bash
python hrms_crewai.py
```

## Project Structure
- `agents/` — CrewAI agent and task definitions
- `GSANZ_HRMS_Leave_Report_Sample.xlsx` — Employee leave data
- `policies/GSANZ_Final_Leave_Policy.pdf` — Leave policy document
- `.env` — Azure OpenAI credentials
- `streamlit_app.py` — Streamlit web interface
- `hrms_crewai.py` — Main CrewAI-based chatbot logic

---

This setup uses CrewAI agents for modular, maintainable orchestration of HRMS queries and is tailored for GSANZ.
