from crewai import Crew
from agents.agents import employee_agent, policy_agent, chatbot_agent
from agents.tasks import (
    create_fetch_employee_leave_task,
    create_fetch_policy_task,
    create_answer_employee_query_task
)

# Create task instances with proper agent assignments
fetch_employee_leave_task = create_fetch_employee_leave_task(employee_agent)
fetch_policy_task = create_fetch_policy_task(policy_agent)

# Create the final task with context from previous tasks
answer_employee_query_task = create_answer_employee_query_task(
    chatbot_agent,
    context_tasks=[fetch_employee_leave_task, fetch_policy_task]
)

crew = Crew(
    agents=[employee_agent, policy_agent, chatbot_agent],
    tasks=[fetch_employee_leave_task, fetch_policy_task, answer_employee_query_task],
    verbose=True,
    process="sequential"  # Ensure tasks run in order
)
