import streamlit as st
from azure_openai import ask_gpt4o

st.set_page_config(page_title="GSANZ HRMS Chatbot", page_icon="🤖")
st.title("🤖 GSANZ HRMS Chatbot")

st.write("""
Ask a question
""")

user_input = st.text_input("Enter your question:")

if st.button("Ask") and user_input:
    with st.spinner("Getting answer"):
        try:
            answer = ask_gpt4o(user_input)
            st.success(answer)
        except Exception as e:
            st.error(f"Error: {e}")
