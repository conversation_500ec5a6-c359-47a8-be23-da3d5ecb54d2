import os
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

EMPLOYEE_CSV = 'employee_leaves_dummy.csv'

# Load employee data
def load_employee_data():
    return pd.read_csv(EMPLOYEE_CSV)

# Get employee leave info
def get_employee_leave_info(employee_id, year, month):
    df = load_employee_data()
    record = df[(df['EmployeeID'] == employee_id) & (df['Year'] == year) & (df['Month'] == month)]
    if record.empty:
        return None
    return record.iloc[0].to_dict()

if __name__ == "__main__":
    # Example usage
    info = get_employee_leave_info('E001', 2025, 'May')
    print(info)
